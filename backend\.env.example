# Google Gemini API Configuration
GOOGLE_API_KEY=your_google_api_key_here

# Qdrant Vector Database Configuration
QDRANT_URL=http://localhost:6333
QDRANT_API_KEY=your_qdrant_api_key_here

# Tavily Search API (for web search)
TAVILY_API_KEY=your_tavily_api_key_here

# ElevenLabs API (for speech synthesis)
ELEVEN_LABS_API_KEY=your_eleven_labs_api_key_here

# Azure Speech Services (for speech-to-text)
AZURE_SPEECH_KEY=your_azure_speech_key_here
AZURE_SPEECH_REGION=your_azure_speech_region_here

# API Configuration
API_BASE_URL=http://localhost:8000

# Hugging Face Token (for reranker models)
HUGGINGFACE_TOKEN=your_huggingface_token_here

# Note: The following Azure OpenAI variables are no longer needed after migration to Gemini:
# deployment_name
# model_name
# azure_endpoint
# openai_api_key
# openai_api_version
# embedding_deployment_name
# embedding_model_name
# embedding_azure_endpoint
# embedding_openai_api_key
# embedding_openai_api_version
