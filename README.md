# 🏥 Medicagent

**Trợ lý y tế thông minh sử dụng kiến trúc đa tác tử (Multi-Agent) với AI**

Medicagent là một hệ thống trợ lý y tế hiện đại được xây dựng với kiến trúc đa tác tử, tích hợp các mô hình AI tiên tiến để hỗ trợ chẩn đoán và tư vấn y tế thông qua giao diện trò chuyện thân thiện và phân tích hình ảnh y tế chuyên sâu.

## 🖼️ Tổng quan hệ thống

![Tổng quan hệ thống Medicagent](backend/assets/Medicagent.png)

## ✨ Tính năng chính

### 💬 Giao diện trò chuyện thông minh
- **Multi-Agent Chat Interface**: <PERSON><PERSON> thống định tuyến thông minh đến các tác tử chuyên biệt
- **Conversation Memory**: <PERSON><PERSON><PERSON> trữ và theo dõi lịch sử hội thoại
- **Real-time Processing**: <PERSON><PERSON> lý tin nhắn và phản hồi theo thời gian thực
- **Session Management**: Quản lý phiên làm việc với cookie-based sessions

### 🖼️ Phân tích hình ảnh y tế AI
- **Brain MRI Analysis**: Phát hiện và phân đoạn khối u não với độ chính xác 97.56%
- **Chest X-ray Analysis**: Chẩn đoán COVID-19 và viêm phổi từ ảnh X-quang ngực (97% accuracy)
- **Skin Lesion Segmentation**: Phân vùng tổn thương da với Dice Score 0.784
- **Bone Fracture Detection**: Phát hiện gãy xương sử dụng YOLOv8
- **Pneumonia Classification**: Phân loại viêm phổi từ ảnh X-quang

### 🎤 Tương tác bằng giọng nói
- **Speech-to-Text**: Chuyển đổi giọng nói thành văn bản (Azure Speech Services)
- **Text-to-Speech**: Tổng hợp giọng nói tự nhiên (ElevenLabs TTS)
- **Voice Recording**: Ghi âm trực tiếp từ trình duyệt
- **Multi-language Support**: Hỗ trợ tiếng Việt và tiếng Anh

### 🔍 Tìm kiếm thông tin y tế
- **RAG System**: Truy xuất thông tin từ tài liệu y khoa đã được xử lý
- **Web Search**: Tìm kiếm thông tin y tế mới nhất từ internet
- **PubMed Integration**: Tìm kiếm nghiên cứu y học từ PubMed
- **Tavily API**: Tìm kiếm đa nguồn thông minh

### ✅ Xác minh con người (Human Validation)
- **Medical Decision Validation**: Xác minh các quyết định y tế quan trọng
- **Expert Review**: Cho phép chuyên gia y tế xem xét và phê duyệt
- **Safety Guardrails**: Hệ thống bảo vệ và kiểm soát chất lượng

## 🏗️ Kiến trúc hệ thống

### Backend Architecture
```
backend/
├── agents/                     # Các tác tử AI chuyên biệt
│   ├── agent_decision.py      # Hệ thống định tuyến tác tử
│   ├── rag_agent/             # Tác tử RAG y tế
│   ├── image_analysis_agent/  # Tác tử phân tích hình ảnh
│   ├── web_search_processor_agent/ # Tác tử tìm kiếm web
│   └── guardrails/            # Hệ thống bảo vệ
├── services/                  # Dịch vụ hỗ trợ
│   └── elevenlabs_tts.py     # Text-to-Speech service
├── data/                      # Dữ liệu và cơ sở tri thức
│   ├── raw/                   # Tài liệu PDF gốc
│   ├── parsed_docs/           # Tài liệu đã xử lý
│   └── qdrant_db/            # Vector database
├── app.py                     # FastAPI application
└── config.py                 # Cấu hình hệ thống
```

### Frontend Architecture
```
frontend/
├── src/
│   ├── app/                   # Next.js App Router
│   ├── components/            # React components
│   │   ├── chat/             # Chat interface components
│   │   └── ui/               # shadcn/ui components
│   ├── lib/                   # Utilities và API clients
│   └── types/                 # TypeScript type definitions
├── public/                    # Static assets
└── package.json              # Dependencies
```

## 🔧 Công nghệ sử dụng

### Frontend Stack
- **Framework**: Next.js 15.3.2 với App Router
- **Language**: TypeScript 5
- **Styling**: Tailwind CSS 4 + shadcn/ui
- **Icons**: Lucide React + Radix UI Icons
- **State Management**: React Hooks
- **HTTP Client**: Fetch API với custom wrappers

### Backend Stack
- **Framework**: FastAPI với Python 3.11
- **AI Orchestration**: LangGraph + LangChain
- **Vector Database**: Qdrant
- **Document Processing**: Docling
- **Computer Vision**: PyTorch + OpenCV
- **Speech Services**: Azure Speech + ElevenLabs
- **Web Search**: Tavily API + PubMed API

### AI Models & Services
| Component | Technology | Purpose |
|-----------|------------|---------|
| 🤖 **Agent Orchestration** | LangGraph | Điều phối và định tuyến giữa các tác tử |
| 🧠 **Language Model** | Google Gemini 2.5 Flash | Xử lý ngôn ngữ tự nhiên và hội thoại |
| � **Document Parsing** | Docling | Phân tích và trích xuất nội dung PDF |
| �️ **Vector Storage** | Qdrant | Lưu trữ và tìm kiếm vector embeddings |
| � **Web Search** | Tavily API + PubMed | Tìm kiếm thông tin y tế trực tuyến |
| 🎤 **Speech-to-Text** | Azure Speech Services | Chuyển đổi giọng nói thành văn bản |
| 🔊 **Text-to-Speech** | ElevenLabs | Tổng hợp giọng nói tự nhiên |
| �️ **Medical Imaging** | Custom PyTorch Models | Phân tích hình ảnh y tế chuyên sâu |

### Medical AI Models
| Model | Architecture | Dataset | Accuracy | Purpose |
|-------|-------------|---------|----------|---------|
| **Brain Tumor Detection** | Custom CNN | MRI Brain Scans | 97.56% | Phát hiện và phân loại khối u não |
| **COVID-19 Detection** | ResNet-based | Chest X-ray Images | 97% | Chẩn đoán COVID-19 từ X-quang ngực |
| **Skin Lesion Segmentation** | U-Net | Dermatology Images | Dice: 0.784 | Phân vùng tổn thương da |
| **Bone Fracture Detection** | YOLOv8 | GRAZPEDWRI-DX | - | Phát hiện gãy xương và bất thường |
| **Pneumonia Classification** | ResNet18 | Chest X-ray Dataset | - | Phân loại viêm phổi |

## 🤖 Hệ thống tác tử (Multi-Agent System)

### 🎯 Agent Decision System
Hệ thống định tuyến thông minh sử dụng LangGraph để phân tích yêu cầu người dùng và định tuyến đến tác tử phù hợp:

- **Input Analysis**: Phân tích nội dung tin nhắn, hình ảnh và ngữ cảnh
- **Smart Routing**: Định tuyến thông minh dựa trên loại yêu cầu
- **Context Awareness**: Hiểu ngữ cảnh hội thoại để đưa ra quyết định chính xác

### � Conversation Agents

#### 🗨️ General Conversation Agent
- Xử lý hội thoại tổng quát và chào hỏi
- Hỗ trợ các câu hỏi không liên quan đến y tế
- Tích hợp với hệ thống guardrails để đảm bảo an toàn

#### 📚 Medical RAG Agent
**Retrieval-Augmented Generation cho y tế**
- **Document Processing**: Xử lý tài liệu PDF y khoa bằng Docling
- **Semantic Chunking**: Chia nhỏ nội dung theo ngữ nghĩa
- **Vector Embeddings**: Tạo embeddings cho tìm kiếm semantic
- **Query Expansion**: Mở rộng truy vấn để tăng độ chính xác
- **Reranking**: Sắp xếp lại kết quả theo độ liên quan
- **Knowledge Base**:
  - Brain tumor research papers
  - COVID-19 chest X-ray studies
  - Deep learning in medical diagnosis
  - Skin lesion analysis research

#### 🌐 Web Search Processor Agent
**Tìm kiếm thông tin y tế trực tuyến**
- **Tavily Integration**: Tìm kiếm đa nguồn thông minh
- **PubMed Search**: Truy cập cơ sở dữ liệu nghiên cứu y học
- **Real-time Information**: Thông tin y tế mới nhất và cập nhật
- **Source Verification**: Xác minh độ tin cậy của nguồn thông tin

### 🖼️ Medical Vision Agents

#### � Brain Tumor Analysis Agent
- **Model**: Custom CNN architecture
- **Input**: Brain MRI images (DICOM, PNG, JPG)
- **Output**: Tumor detection, classification, and segmentation
- **Classes**: Glioma, Meningioma, Pituitary tumor, No tumor
- **Accuracy**: 97.56%
- **Features**:
  - Tumor localization and size estimation
  - Confidence scoring
  - Visual overlay of detected regions

#### 🫁 Chest X-ray Analysis Agent
- **Model**: ResNet-based classification
- **Input**: Chest X-ray images
- **Output**: COVID-19 detection and pneumonia classification
- **Classes**: COVID-19, Normal, Pneumonia
- **Accuracy**: 97% (COVID-19 detection)
- **Features**:
  - Multi-class disease detection
  - Confidence scoring
  - Clinical recommendations

#### 🦠 Skin Lesion Segmentation Agent
- **Model**: U-Net architecture
- **Input**: Dermatology images
- **Output**: Lesion segmentation masks
- **Performance**: Dice Score 0.784
- **Features**:
  - Precise lesion boundary detection
  - Visual overlay generation
  - Area calculation and analysis

#### 🦴 Bone Fracture Detection Agent
- **Model**: YOLOv8 object detection
- **Input**: X-ray bone images
- **Output**: Fracture detection and localization
- **Classes**: 9 bone abnormality types
- **Features**:
  - Multi-class bone abnormality detection
  - Bounding box visualization
  - Confidence scoring per detection

#### 🫁 Pneumonia Classification Agent
- **Model**: ResNet18 binary classifier
- **Input**: Chest X-ray images (grayscale optimized)
- **Output**: Pneumonia vs Normal classification
- **Features**:
  - Binary classification with confidence
  - Optimized for grayscale X-ray images
  - Clinical decision support

## 🛡️ Hệ thống bảo mật và xác minh

### Human Validation System
- **Critical Decision Review**: Tất cả các quyết định y tế quan trọng đều yêu cầu xác minh từ con người
- **Expert Approval Workflow**: Quy trình phê duyệt từ chuyên gia y tế
- **Validation Timeout**: Hệ thống timeout 300 giây cho các quyết định xác minh
- **Safety First**: Mặc định từ chối các quyết định chưa được xác minh

### Guardrails System
- **Content Filtering**: Lọc nội dung không phù hợp hoặc nguy hiểm
- **Medical Ethics**: Tuân thủ các nguyên tắc đạo đức y tế
- **Response Validation**: Kiểm tra tính chính xác của phản hồi
- **Risk Assessment**: Đánh giá rủi ro trước khi đưa ra khuyến nghị

## 🚀 Cài đặt và triển khai

### Yêu cầu hệ thống
- **Node.js**: 18.0+ (cho frontend)
- **Python**: 3.11+ (cho backend)
- **RAM**: Tối thiểu 8GB (khuyến nghị 16GB cho các mô hình AI)
- **Storage**: 10GB+ (cho models và dữ liệu)
- **GPU**: Khuyến nghị NVIDIA GPU với CUDA support

### Biến môi trường cần thiết

Tạo file `.env` trong thư mục `backend/`:

```bash
# Google Gemini API
GOOGLE_API_KEY=your_google_api_key_here

# Azure Speech Services
AZURE_SPEECH_KEY=your_azure_speech_key
AZURE_SPEECH_REGION=your_azure_region

# ElevenLabs TTS
ELEVEN_LABS_API_KEY=your_elevenlabs_api_key

# Tavily Search API
TAVILY_API_KEY=your_tavily_api_key

# API Configuration
API_BASE_URL=http://localhost:8000
```

### 🖥️ Cài đặt Development Environment

#### 1. Clone Repository
```bash
git clone <repository-url>
cd Medicagent
```

#### 2. Backend Setup
```bash
cd backend

# Tạo virtual environment
python -m venv venv

# Kích hoạt virtual environment
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate

# Cài đặt dependencies
pip install -r requirements.txt

# Khởi tạo cơ sở dữ liệu vector (nếu cần)
python ingest_rag_data.py

# Chạy backend server
python app.py
```

#### 3. Frontend Setup
```bash
cd frontend

# Cài đặt dependencies
npm install

# Chạy development server
npm run dev
```

### 🐳 Triển khai với Docker

#### Backend Docker
```bash
cd backend

# Build Docker image
docker build -t medicagent-backend .

# Run container
docker run -p 8000:8000 \
  -e GOOGLE_API_KEY=your_key \
  -e AZURE_SPEECH_KEY=your_key \
  -e AZURE_SPEECH_REGION=your_region \
  -e ELEVEN_LABS_API_KEY=your_key \
  -e TAVILY_API_KEY=your_key \
  medicagent-backend
```

#### Production Deployment
```bash
# Frontend build
cd frontend
npm run build
npm start

# Backend production
cd backend
gunicorn -w 4 -k uvicorn.workers.UvicornWorker app:app --bind 0.0.0.0:8000
```

## 📡 API Documentation

### Core Endpoints

#### Chat API
```http
POST /api/chat
Content-Type: application/json

{
  "query": "Tôi có triệu chứng đau đầu, có thể là gì?",
  "conversation_history": []
}
```

#### Image Upload API
```http
POST /api/upload
Content-Type: multipart/form-data

file: <image_file>
query: "Phân tích ảnh X-quang này"
```

#### Speech Processing
```http
POST /api/transcribe
Content-Type: multipart/form-data

audio: <audio_file>
```

```http
POST /api/speech
Content-Type: application/json

{
  "text": "Kết quả phân tích cho thấy...",
  "voice_id": "optional_voice_id"
}
```

#### Human Validation
```http
POST /api/validate
Content-Type: application/json

{
  "validation": "approve|reject",
  "comments": "Nhận xét từ chuyên gia"
}
```

### Response Format
```json
{
  "status": "success|error",
  "response": "Phản hồi từ hệ thống",
  "agent": "Tên tác tử đã xử lý",
  "result_image": "URL ảnh kết quả (nếu có)",
  "confidence": 0.95,
  "processing_time": 1.23
}
```

## 🧪 Testing và Quality Assurance

### Sample Images
Hệ thống cung cấp các ảnh mẫu để test:
- `backend/sample_images/brain_tumor/` - Ảnh MRI não
- `backend/sample_images/chest_x-ray_covid_and_normal/` - Ảnh X-quang ngực
- `backend/sample_images/skin_lesion_images/` - Ảnh tổn thương da
- `backend/sample_images/bone_fracture/` - Ảnh X-quang xương
- `backend/sample_images/chest_x-ray_pneumonia/` - Ảnh X-quang viêm phổi

### Health Check
```bash
curl http://localhost:8000/health
```

### Model Performance Testing
```bash
# Test individual agents
python -m agents.image_analysis_agent.brain_tumor_agent.brain_tumor_inference
python -m agents.image_analysis_agent.chest_xray_agent.covid_chest_xray_inference
```
## 📊 Cơ sở dữ liệu và tri thức

### Medical Knowledge Base
Hệ thống được trang bị cơ sở tri thức y tế phong phú:

#### Tài liệu đã được xử lý (RAG Database)
- **Brain Tumor Research**: Nghiên cứu về khối u não và kỹ thuật deep learning
- **COVID-19 Studies**: Tài liệu về chẩn đoán COVID-19 từ X-quang ngực
- **Skin Lesion Analysis**: Nghiên cứu về phân tích tổn thương da
- **Bone Fracture Detection**: Tài liệu về phát hiện gãy xương
- **General Medical Knowledge**: Kiến thức y tế tổng quát

#### Vector Database (Qdrant)
- **Semantic Search**: Tìm kiếm theo ngữ nghĩa
- **High-dimensional Embeddings**: Vector embeddings chất lượng cao
- **Fast Retrieval**: Truy xuất nhanh chóng và chính xác
- **Scalable Storage**: Lưu trữ có thể mở rộng

### Data Processing Pipeline
```
PDF Documents → Docling Parser → Semantic Chunking → Vector Embeddings → Qdrant Storage
```

## 🔄 Workflow và quy trình

### User Interaction Flow
```mermaid
graph TD
    A[User Input] --> B{Input Type?}
    B -->|Text| C[Agent Decision System]
    B -->|Image| D[Image Classification]
    B -->|Voice| E[Speech-to-Text]

    C --> F{Route to Agent}
    F -->|General| G[Conversation Agent]
    F -->|Medical Knowledge| H[RAG Agent]
    F -->|Recent Info| I[Web Search Agent]

    D --> J{Image Type?}
    J -->|Brain MRI| K[Brain Tumor Agent]
    J -->|Chest X-ray| L[Chest X-ray Agent]
    J -->|Skin Image| M[Skin Lesion Agent]
    J -->|Bone X-ray| N[Bone Fracture Agent]

    G --> O[Response]
    H --> P{Needs Validation?}
    I --> O
    K --> P
    L --> P
    M --> P
    N --> P

    P -->|Yes| Q[Human Validation]
    P -->|No| O
    Q --> O

    E --> C
    O --> R[User Response]
```

### Agent Decision Logic
1. **Input Analysis**: Phân tích loại input (text, image, voice)
2. **Context Evaluation**: Đánh giá ngữ cảnh hội thoại
3. **Agent Selection**: Chọn tác tử phù hợp nhất
4. **Processing**: Xử lý yêu cầu bởi tác tử được chọn
5. **Validation Check**: Kiểm tra nhu cầu xác minh con người
6. **Response Generation**: Tạo phản hồi cuối cùng

## 🛠️ Cấu hình nâng cao

### Model Configuration
```python
# config.py - Medical CV Models
BRAIN_TUMOR_MODEL = "./agents/image_analysis_agent/brain_tumor_agent/models/brain_tumor_segmentation.pth"
CHEST_XRAY_MODEL = "./agents/image_analysis_agent/chest_xray_agent/models/covid_chest_xray_model.pth"
SKIN_LESION_MODEL = "./agents/image_analysis_agent/skin_lesion_agent/models/checkpointN25_.pth.tar"
BONE_FRACTURE_MODEL = "./agents/image_analysis_agent/bone_fracture_agent/models/bone_fracture_yolov8.pt"
PNEUMONIA_MODEL = "./agents/image_analysis_agent/pneumonia_agent/models/pneumonia_model.pth"
```

### RAG Configuration
```python
# Vector Database Settings
QDRANT_HOST = "localhost"
QDRANT_PORT = 6333
COLLECTION_NAME = "medical_documents"
VECTOR_SIZE = 768
DISTANCE_METRIC = "cosine"

# Retrieval Settings
TOP_K_RETRIEVAL = 10
RERANK_TOP_K = 5
MIN_CONFIDENCE = 0.7
CONTEXT_LIMIT = 10
```

### Agent Validation Settings
```python
# Human validation requirements
VALIDATION_REQUIRED = {
    "BRAIN_TUMOR_AGENT": True,
    "CHEST_XRAY_AGENT": True,
    "SKIN_LESION_AGENT": True,
    "BONE_FRACTURE_AGENT": True,
    "PNEUMONIA_AGENT": True,
    "CONVERSATION_AGENT": False,
    "RAG_AGENT": False,
    "WEB_SEARCH_AGENT": False
}
```

## 🚨 Troubleshooting

### Common Issues

#### Backend Issues
```bash
# Model loading errors
Error: Model file not found
Solution: Ensure model files are downloaded and paths are correct

# Qdrant connection issues
Error: Connection refused to Qdrant
Solution: Start Qdrant service or check connection settings

# API key errors
Error: Invalid API key
Solution: Check .env file and API key validity
```

#### Frontend Issues
```bash
# CORS errors
Error: CORS policy blocked
Solution: Check backend CORS settings in app.py

# API connection issues
Error: Failed to fetch
Solution: Ensure backend is running on correct port (8000)
```

### Debug Mode
```bash
# Enable debug logging
export DEBUG=true
python app.py

# Check logs
tail -f logs/medicagent.log
```

## 📈 Performance và tối ưu hóa

### Model Performance Metrics
| Agent | Metric | Score | Dataset |
|-------|--------|-------|---------|
| Brain Tumor | Accuracy | 97.56% | MRI Brain Scans |
| COVID-19 Detection | Accuracy | 97% | Chest X-ray Images |
| Skin Lesion | Dice Score | 0.784 | Dermatology Dataset |
| Bone Fracture | mAP@0.5 | - | GRAZPEDWRI-DX |
| Pneumonia | Accuracy | - | Chest X-ray Dataset |

### System Performance
- **Response Time**: < 2 seconds (text queries)
- **Image Processing**: 3-10 seconds (depending on model)
- **Concurrent Users**: Up to 100 simultaneous connections
- **Memory Usage**: 4-8GB (with all models loaded)
- **Storage**: ~5GB (models + knowledge base)
## 🔐 Bảo mật và tuân thủ

### Data Privacy
- **Local Processing**: Tất cả dữ liệu y tế được xử lý cục bộ
- **No Data Retention**: Không lưu trữ dữ liệu cá nhân sau khi xử lý
- **Secure Upload**: Upload file được mã hóa và tự động xóa
- **Session Security**: Quản lý phiên làm việc an toàn

### Medical Compliance
- **Disclaimer**: Hệ thống chỉ hỗ trợ tham khảo, không thay thế chẩn đoán y tế
- **Human Oversight**: Yêu cầu xác minh từ chuyên gia cho các quyết định quan trọng
- **Audit Trail**: Ghi lại tất cả các quyết định và xác minh
- **Error Handling**: Xử lý lỗi an toàn và thông báo rõ ràng

## 🎯 Use Cases và ví dụ

### 1. Phân tích ảnh MRI não
```
User: [Upload brain MRI image]
System: → Brain Tumor Agent
Output: "Phát hiện khối u loại Glioma với độ tin cậy 94.2%. Khuyến nghị tham khảo bác sĩ chuyên khoa thần kinh."
Validation: Required ✅
```

### 2. Tư vấn triệu chứng COVID-19
```
User: "Tôi có triệu chứng ho, sốt và khó thở. Có phải COVID-19 không?"
System: → RAG Agent + Web Search Agent
Output: "Dựa trên triệu chứng mô tả, có thể là COVID-19. Khuyến nghị test nhanh và cách ly. [Kèm thông tin từ WHO]"
```

### 3. Phân tích tổn thương da
```
User: [Upload skin lesion image] "Đây có phải là ung thư da không?"
System: → Skin Lesion Agent
Output: "Phân vùng tổn thương hoàn tất. Diện tích: 2.3cm². Khuyến nghị khám da liễu để đánh giá chính xác."
Validation: Required ✅
```

## 🔧 Maintenance và cập nhật

### Model Updates
```bash
# Update medical models
python utils/download_models.py --update-all

# Retrain specific model
python agents/image_analysis_agent/brain_tumor_agent/train.py

# Validate model performance
python utils/model_validation.py
```

### Knowledge Base Updates
```bash
# Add new medical documents
python ingest_rag_data.py --add-documents /path/to/new/docs

# Reset and rebuild knowledge base
python reset_qdrant.py
python ingest_rag_data.py
```

### System Monitoring
```bash
# Check system health
curl http://localhost:8000/health

# Monitor resource usage
python utils/system_monitor.py

# Check agent performance
python utils/agent_metrics.py
```

## 🤝 Contributing

### Development Guidelines
1. **Code Style**: Tuân thủ PEP 8 cho Python, ESLint cho TypeScript
2. **Testing**: Viết unit tests cho tất cả các tính năng mới
3. **Documentation**: Cập nhật documentation cho mọi thay đổi
4. **Medical Accuracy**: Đảm bảo tính chính xác y tế trong mọi thay đổi

### Adding New Agents
```python
# 1. Create agent class
class NewMedicalAgent:
    def __init__(self, config):
        # Initialize agent
        pass

    def process(self, input_data):
        # Process medical data
        return result

# 2. Register in agent_decision.py
def run_new_agent(state: AgentState) -> AgentState:
    # Agent implementation
    pass

# 3. Add to workflow
workflow.add_node("NEW_AGENT", run_new_agent)
```

### Adding New Medical Models
```python
# 1. Implement model class
class NewMedicalModel:
    def __init__(self, model_path):
        self.model = self.load_model(model_path)

    def predict(self, image_path):
        # Model inference
        return prediction

# 2. Integrate with ImageAnalysisAgent
# 3. Add validation requirements
# 4. Update configuration
```

## 📚 Tài liệu tham khảo

### Medical Research Papers
- Brain Tumor Detection using Deep Learning
- COVID-19 Detection from Chest X-rays
- Skin Lesion Segmentation with U-Net
- Bone Fracture Detection using YOLO

### Technical Documentation
- [LangGraph Documentation](https://langchain-ai.github.io/langgraph/)
- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [Next.js Documentation](https://nextjs.org/docs)
- [Qdrant Documentation](https://qdrant.tech/documentation/)

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](backend/LICENSE) file for details.

## 🙏 Acknowledgments

- **Medical Datasets**: Cảm ơn các nhà nghiên cứu đã cung cấp datasets y tế
- **Open Source Community**: Cảm ơn cộng đồng mã nguồn mở
- **Medical Experts**: Cảm ơn các chuyên gia y tế đã tư vấn và xác minh

## 📞 Liên hệ và hỗ trợ

- **Issues**: Báo cáo lỗi qua GitHub Issues
- **Discussions**: Thảo luận tính năng qua GitHub Discussions
- **Email**: <EMAIL> (nếu có)

---

**⚠️ Lưu ý quan trọng**: Medicagent là công cụ hỗ trợ tham khảo và không thay thế cho việc khám bệnh, chẩn đoán hoặc điều trị y tế chuyên nghiệp. Luôn tham khảo ý kiến bác sĩ cho các vấn đề sức khỏe.