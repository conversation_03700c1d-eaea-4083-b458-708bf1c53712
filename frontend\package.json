{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-slot": "^1.2.2", "@types/marked": "^5.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.508.0", "marked": "^15.0.11", "next": "15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.2.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "tw-animate-css": "^1.2.9", "typescript": "^5"}}